# DSTweaks Multi-Directory Sync Helper
# Manages synchronization between multiple local directories for different branches

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("status", "sync-all", "push-to-remote", "pull-from-remote")]
    [string]$Action
)

# Directory mappings
$Directories = @{
    "main" = "C:\wamp64\www\DSTweaks\wp-content\plugins\DSTweaks"
    "mvmdashti" = "C:\wamp64\www\mvmdashti.ir\wp-content\plugins\DSTweaks"
    "imico" = "C:\wamp64\www\imico\wp-content\plugins\DSTweaks"
}

function Show-MultiDirectoryStatus {
    Write-Host "=== DSTweaks Multi-Directory Status ===" -ForegroundColor Green
    Write-Host ""
    
    foreach ($branch in $Directories.Keys) {
        $path = $Directories[$branch]
        Write-Host "Branch: $branch" -ForegroundColor Cyan
        Write-Host "Path: $path" -ForegroundColor Yellow
        
        if (Test-Path $path) {
            Push-Location $path
            try {
                $currentBranch = git branch --show-current
                $status = git status --porcelain
                $lastCommit = git log -1 --oneline
                
                Write-Host "  Current Branch: $currentBranch" -ForegroundColor White
                Write-Host "  Last Commit: $lastCommit" -ForegroundColor Gray
                
                if ($status) {
                    Write-Host "  Status: UNCOMMITTED CHANGES" -ForegroundColor Red
                    Write-Host "  Changes:" -ForegroundColor Red
                    $status | ForEach-Object { Write-Host "    $_" -ForegroundColor Red }
                } else {
                    Write-Host "  Status: CLEAN" -ForegroundColor Green
                }
            }
            finally {
                Pop-Location
            }
        } else {
            Write-Host "  Status: DIRECTORY NOT FOUND" -ForegroundColor Red
        }
        Write-Host ""
    }
}

function Sync-AllDirectories {
    Write-Host "=== Syncing All Directories ===" -ForegroundColor Green
    Write-Host ""
    
    foreach ($branch in $Directories.Keys) {
        $path = $Directories[$branch]
        Write-Host "Syncing $branch at $path" -ForegroundColor Cyan
        
        if (Test-Path $path) {
            Push-Location $path
            try {
                # Fetch latest changes
                Write-Host "  Fetching latest changes..." -ForegroundColor Yellow
                git fetch origin
                
                # Check if we're on the correct branch
                $currentBranch = git branch --show-current
                if ($currentBranch -ne $branch) {
                    Write-Host "  Switching to $branch branch..." -ForegroundColor Yellow
                    git checkout $branch
                }
                
                # Pull latest changes
                Write-Host "  Pulling latest changes..." -ForegroundColor Yellow
                git pull origin $branch
                
                Write-Host "  ✅ $branch synced successfully" -ForegroundColor Green
            }
            catch {
                Write-Host "  ❌ Error syncing $branch`: $_" -ForegroundColor Red
            }
            finally {
                Pop-Location
            }
        } else {
            Write-Host "  ❌ Directory not found: $path" -ForegroundColor Red
        }
        Write-Host ""
    }
}

function Push-AllToRemote {
    Write-Host "=== Pushing All Directories to Remote ===" -ForegroundColor Green
    Write-Host ""
    
    foreach ($branch in $Directories.Keys) {
        $path = $Directories[$branch]
        Write-Host "Pushing $branch from $path" -ForegroundColor Cyan
        
        if (Test-Path $path) {
            Push-Location $path
            try {
                # Check if there are any changes to commit
                $status = git status --porcelain
                if ($status) {
                    Write-Host "  Found uncommitted changes, committing..." -ForegroundColor Yellow
                    git add .
                    $commitMessage = Read-Host "  Enter commit message for $branch"
                    git commit -m $commitMessage
                }
                
                # Push to remote
                Write-Host "  Pushing to remote..." -ForegroundColor Yellow
                git push origin $branch
                
                Write-Host "  ✅ $branch pushed successfully" -ForegroundColor Green
            }
            catch {
                Write-Host "  ❌ Error pushing $branch`: $_" -ForegroundColor Red
            }
            finally {
                Pop-Location
            }
        } else {
            Write-Host "  ❌ Directory not found: $path" -ForegroundColor Red
        }
        Write-Host ""
    }
}

function Pull-AllFromRemote {
    Write-Host "=== Pulling All Directories from Remote ===" -ForegroundColor Green
    Write-Host ""
    
    foreach ($branch in $Directories.Keys) {
        $path = $Directories[$branch]
        Write-Host "Pulling $branch to $path" -ForegroundColor Cyan
        
        if (Test-Path $path) {
            Push-Location $path
            try {
                # Fetch and pull latest changes
                Write-Host "  Fetching and pulling latest changes..." -ForegroundColor Yellow
                git fetch origin
                git pull origin $branch
                
                Write-Host "  ✅ $branch pulled successfully" -ForegroundColor Green
            }
            catch {
                Write-Host "  ❌ Error pulling $branch`: $_" -ForegroundColor Red
            }
            finally {
                Pop-Location
            }
        } else {
            Write-Host "  ❌ Directory not found: $path" -ForegroundColor Red
        }
        Write-Host ""
    }
}

# Main script logic
switch ($Action) {
    "status" {
        Show-MultiDirectoryStatus
    }
    "sync-all" {
        Sync-AllDirectories
    }
    "push-to-remote" {
        Push-AllToRemote
    }
    "pull-from-remote" {
        Pull-AllFromRemote
    }
}

Write-Host "=== Multi-Directory Sync Complete ===" -ForegroundColor Green
