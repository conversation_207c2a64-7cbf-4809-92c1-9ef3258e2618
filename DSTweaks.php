<?php
/**
 * Plugin Name: DSTweaks
 * Description: "DSDev Tweaks" plugin provides a modular core system for loading and executing custom features for different projects.
 * Version: 3.0.0
 * Author: <PERSON><PERSON><PERSON>
 * Author URI: https://dsdev.ir
 * License: GPL2
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: dstweaks
 * Domain Path: /languages
 */

// Security: Prevent direct file access
defined('ABSPATH') || exit;

// Define main plugin file constant for use in other plugin files
if (!defined('DSTWEAKS_MAIN_FILE')) {
    define('DSTWEAKS_MAIN_FILE', __FILE__);
}

/**
 * Add security headers to WordPress HTTP response
 *
 * @param array $headers Existing HTTP headers array
 * @return array Modified headers array with security headers added
 */
function dstweaks_security_headers($headers) {
    $headers['X-Content-Type-Options'] = 'nosniff';
    $headers['X-Frame-Options'] = 'SAMEORIGIN';
    $headers['X-XSS-Protection'] = '1; mode=block';
    $headers['Referrer-Policy'] = 'strict-origin-when-cross-origin';
    return $headers;
}
add_filter('wp_headers', 'dstweaks_security_headers');

/**
 * Load plugin textdomain for internationalization
 *
 * @return void
 */
function dstweaks_load_textdomain() {
    load_plugin_textdomain('dstweaks', false, dirname(plugin_basename(__FILE__)) . DIRECTORY_SEPARATOR .'languages');
}
add_action('init', 'dstweaks_load_textdomain');

/**
 * Clear all plugin transients and caches
 *
 * @return bool True if caches were cleared successfully, false otherwise
 */
function dstweaks_clear_all_caches() {
    global $wpdb;
    try {
        // Delete all transients with our prefix
        $result = $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->options} 
                WHERE option_name LIKE %s 
                OR option_name LIKE %s",
                $wpdb->esc_like('_transient_dstweaks_') . '%',
                $wpdb->esc_like('_transient_timeout_dstweaks_') . '%'
            )
        );

        if ($result === false) {
            if (WP_DEBUG) {
                error_log(sprintf('[DSTweaks] Error clearing transients from database'));
            }
            return false;
        }

        // Clear any cached data in memory
        wp_cache_flush();

        return true;
    } catch (Exception $e) {
        if (WP_DEBUG) {
            error_log(sprintf('[DSTweaks] Exception while clearing caches: %s', $e->getMessage()));
        }
        return false;
    }
}

/**
 * Cleanup function for failed module loading
 *
 * Removes temporary data and module-specific options when a module fails to load
 *
 * @param string $module The module name to clean up
 * @return void
 */
function dstweaks_cleanup_failed_module($module) {
    // Clear any temporary data
    $temp_data_key = 'dstweaks_module_temp_' . $module;
    delete_transient($temp_data_key);
    
    // Clear any module-specific options
    $module_options = get_option('dstweaks_module_' . $module . '_options');
    if ($module_options) {
        delete_option('dstweaks_module_' . $module . '_options');
    }
    
    if (WP_DEBUG) {
        error_log(sprintf('[DSTweaks] Cleaned up failed module: %s', $module));
    }
}

/**
 * Custom error handler for DSTweaks plugin
 *
 * Logs errors that occur within DSTweaks plugin files when WP_DEBUG is enabled
 *
 * @param int    $errno   Error level
 * @param string $errstr  Error message
 * @param string $errfile File where error occurred
 * @param int    $errline Line number where error occurred
 * @return bool False to continue with normal error handling
 */
function dstweaks_error_handler($errno, $errstr, $errfile, $errline) {
    if (WP_DEBUG) {
        // Only log errors from DSTweaks plugin files
        if (strpos($errfile, 'DSTweaks') !== false) {
            error_log(sprintf('[DSTweaks] Error: [%d] %s in %s on line %d', $errno, $errstr, $errfile, $errline));
        }
    }
    return false;
}
set_error_handler('dstweaks_error_handler', E_ALL);

// Load admin page
if (is_admin()) {
    require_once plugin_dir_path(__FILE__) . 'common/admin.php';
}

/**
 * Populate global modules array with module information
 *
 * Reads the manifest file and populates the global $dstweaks_modules array
 * with module metadata including name, description, version, and status
 *
 * @return void
 */
function dstweaks_populate_modules() {
    $GLOBALS['dstweaks_modules'] = array();

    $modules_dir = plugin_dir_path(__FILE__) . 'modules'. DIRECTORY_SEPARATOR;
    $manifest_file = $modules_dir . 'manifest.json';

    // Include manifest file modification time in cache key so updates invalidate the transient
    $manifest_mtime = is_file($manifest_file) ? filemtime($manifest_file) : 0;
    $manifest_cache_key = 'dstweaks_manifest_' . md5($manifest_file . '|' . $manifest_mtime);
    $manifest = get_transient($manifest_cache_key);

    if (false === $manifest) {
        if (!is_file($manifest_file)) {
            $GLOBALS['dstweaks_modules']['core'] = 'manifest_missing';
            if (WP_DEBUG) {
                error_log(sprintf('[DSTweaks] manifest_missing: %s', $manifest_file));
            }
            return;
        }

        $manifest_content = file_get_contents($manifest_file);
        if ($manifest_content === false || !is_string($manifest_content)) {
            $GLOBALS['dstweaks_modules']['core'] = 'manifest_unreadable';
            if (WP_DEBUG) {
                error_log(sprintf('[DSTweaks] manifest_unreadable: %s', $manifest_file));
            }
            return;
        }

        $manifest = json_decode($manifest_content, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            $GLOBALS['dstweaks_modules']['core'] = 'manifest_invalid';
            if (WP_DEBUG) {
                error_log(sprintf('[DSTweaks] manifest_invalid: %s - %s', json_last_error_msg(), $manifest_file));
            }
            return;
        }

        // Cache manifest for 1 hour so populate and load use the same cached data
        set_transient($manifest_cache_key, $manifest, HOUR_IN_SECONDS);
    }

    foreach ($manifest as $module => $details) {
        $module_path = realpath($modules_dir . $module . DIRECTORY_SEPARATOR . $module . '.php');
        $module_data = array(
            'name' => ucfirst($module) . ' Module',
            'description' => '',
            'version' => '',
            'status' => 'inactive',
            'errors' => '',
            'messages' => array()
        );

        if ($module_path !== false && strpos($module_path, realpath($modules_dir)) === 0) {
            $file_data = get_file_data($module_path, array('Version' => 'Version', 'Description' => 'Description'));
            $module_data['description'] = $file_data['Description'];
            $module_data['version'] = $file_data['Version'];
        } else {
            $module_data['errors'] = 'invalid_path';
        }

        $GLOBALS['dstweaks_modules'][$module] = $module_data;
    }
}
add_action('plugins_loaded', 'dstweaks_populate_modules', 5);

/**
 * Load and validate modules based on manifest configuration
 *
 * Checks memory usage, reads manifest file, validates module files,
 * and loads enabled modules while performing security checks
 *
 * @return void
 */
function dstweaks_load_modules() {
    // Check memory limit
    $memory_limit = wp_convert_hr_to_bytes(ini_get('memory_limit'));
    $memory_usage = memory_get_usage(true);
    if ($memory_usage >= $memory_limit * 0.9) {
        if (WP_DEBUG) {
            error_log(sprintf('[DSTweaks] Memory usage too high, skipping module loading'));
        }
        return;
    }

    $modules_dir = plugin_dir_path(__FILE__) . 'modules' . DIRECTORY_SEPARATOR;
    $manifest_file = $modules_dir . 'manifest.json';

    // Try to get manifest from cache first. Include mtime to ensure manifest updates are fetched.
    $manifest_mtime = is_file($manifest_file) ? filemtime($manifest_file) : 0;
    $manifest_cache_key = 'dstweaks_manifest_' . md5($manifest_file . '|' . $manifest_mtime);
    $manifest = get_transient($manifest_cache_key);
    
    if (false === $manifest) {
        if (!is_file($manifest_file) || !is_readable($manifest_file)) {
            $GLOBALS['dstweaks_modules']['core'] = 'manifest_unreadable';
            if (WP_DEBUG) {
                error_log(sprintf('[DSTweaks] manifest_unreadable: %s', $manifest_file));
            }
            return;
        }

        $manifest_content = file_get_contents($manifest_file);
        if (false === $manifest_content) {
            $GLOBALS['dstweaks_modules']['core'] = 'manifest_unreadable';
            if (WP_DEBUG) {
                error_log(sprintf('[DSTweaks] manifest_unreadable: %s', $manifest_file));
            }
            return;
        }

        $manifest = json_decode($manifest_content, true);
        if (JSON_ERROR_NONE !== json_last_error()) {
            $GLOBALS['dstweaks_modules']['core'] = 'manifest_invalid';
            if (WP_DEBUG) {
                error_log(sprintf('[DSTweaks] manifest_invalid: %s - %s', json_last_error_msg(), $manifest_file));
            }
            return;
        }
        
        // Cache manifest for 1 hour
        set_transient($manifest_cache_key, $manifest, HOUR_IN_SECONDS);
    }

    // Get enabled/disabled states from option
    $module_states = get_option('dstweaks_module_states', array());

    foreach ($GLOBALS['dstweaks_modules'] as $module => &$module_data) {
        if ($module == 'core') continue;
        // If module is disabled, skip loading
        if (isset($module_states[$module]) && $module_states[$module] === '0') {
            $module_data['status'] = 'disabled';
            continue;
        }
        $module_path = realpath($modules_dir . $module . DIRECTORY_SEPARATOR . $module . '.php');
        $details = $manifest[$module];

        // Normalize expected hashes from manifest to lowercase so comparisons are case-insensitive.
        $expected_hash = isset($details['hash']) ? strtolower($details['hash']) : '';
        $expected_manifest_hash = isset($details['manifest_hash']) ? strtolower($details['manifest_hash']) : '';

        if (WP_DEBUG) {
            error_log(sprintf('[DSTweaks] Loading module %s from %s', $module, $module_path));
        }
        if ($module_data['version'] !== $details['version']) {
            $module_data['errors'] = 'version_mismatch';
            $module_data['status'] = 'external_error';
            if (WP_DEBUG) {
                error_log(sprintf('[DSTweaks] Version mismatch for %s: file=%s, manifest=%s', 
                    $module, 
                    $module_data['version'], 
                    $details['version']
                ));
            }
            continue;
        }

        if (!empty($details['manifest_hash'])) {
            $manifest_file_name = strtolower($module) . 'manifest.json';
            $manifest_file_path = $modules_dir . $module . DIRECTORY_SEPARATOR . $manifest_file_name;
            if (!is_file($manifest_file_path)) {
                $module_data['errors'] = 'manifest_file_missing';
                $module_data['status'] = 'external_error';
                if (WP_DEBUG) {
                    error_log(sprintf('[DSTweaks] Manifest file missing for %s: %s', 
                        $module, 
                        $manifest_file_path
                    ));
                }
                dstweaks_cleanup_failed_module($module);
                continue;
            }
            $manifest_hash = hash_file('sha256', $manifest_file_path);
            if ($expected_manifest_hash === '' || !hash_equals($manifest_hash, $expected_manifest_hash)) {
                $module_data['errors'] = 'manifest_hash_mismatch';
                $module_data['status'] = 'external_error';
                if (WP_DEBUG) {
                    error_log(sprintf('[DSTweaks] Manifest hash mismatch for %s: file=%s, manifest=%s',
                        $module,
                        $manifest_hash,
                        $details['manifest_hash']
                    ));
                }
                continue;
            }
        }

        if (is_file($module_path)) {
            $file_hash = hash_file('sha256', $module_path);
            if ($expected_hash !== '' && hash_equals($file_hash, $expected_hash)) {
                if ($module_data['errors']) {
                    $module_data['status'] = 'external_error';
                    if (WP_DEBUG) {
                        error_log(sprintf('[DSTweaks] Skipping %s due to error: %s', $module, $module_data['errors']));
                    }
                    continue;
                }
                require_once $module_path;
                $module_data['status'] = 'active';
                // Check for internal errors after loading
                if ($module_data['errors']) {
                    $module_data['status'] = 'internal_error';
                    if (WP_DEBUG) {
                        error_log(sprintf('[DSTweaks] Module %s is having internal error: %s', $module, $module_data['errors']));
                    }
                    continue;
                }
            } else {
                $module_data['status'] = 'external_error';
                $module_data['errors'] = 'hash_mismatch';
                if (WP_DEBUG) {
                    error_log(sprintf('[DSTweaks] Hash mismatch for %s', $module));
                }
                continue;
            }
        } else {
            if (WP_DEBUG) {
                error_log(sprintf('[DSTweaks] Module file missing for %s: %s', $module, $module_path));
            }
            $module_data['status'] = 'external_error';
            $module_data['errors'] = 'file_missing';
            continue;
        }
    }
}
add_action('plugins_loaded', 'dstweaks_load_modules', 10);
?>
